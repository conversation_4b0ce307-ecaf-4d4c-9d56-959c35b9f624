﻿# Agent Zero - Gemini Edition

A complete replica of Agent Zero powered exclusively by Google Gemini AI models.

## Features

- **Gemini AI Integration**: Uses Gemini 2.0 Flash and 1.5 Flash models
- **Multi-Agent System**: Hierarchical agent architecture
- **Tool Execution**: Code execution, web browsing, memory management
- **Web Interface**: Real-time streaming UI
- **Memory System**: Persistent memory and knowledge base
- **MCP Support**: Model Context Protocol integration

## Quick Start

1. Install dependencies:
`ash
pip install -r requirements.txt
`

2. Set up your environment:
`ash
cp example.env .env
# Edit .env with your Gemini API key
`

3. Run the web interface:
`ash
python run_ui.py
`

4. Open http://localhost:50001 in your browser

## Configuration

Set your Gemini API key in the .env file:
`
GEMINI_API_KEY=your_api_key_here
`

## Models Used

- **Chat Model**: gemini-2.0-flash-exp
- **Utility Model**: gemini-1.5-flash  
- **Browser Model**: gemini-2.0-flash-exp
- **Embedding Model**: text-embedding-004

## Architecture

This is a complete replica of Agent Zero with the following key components:

- gent.py - Core agent framework
- models.py - Gemini AI integration
- python/tools/ - Agent tools and capabilities
- python/helpers/ - Utility functions
- webui/ - Web interface
- prompts/ - System prompts and templates

## License

Same as original Agent Zero
