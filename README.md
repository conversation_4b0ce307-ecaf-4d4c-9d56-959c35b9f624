﻿# Agent Zero - Gemini Edition

A complete replica of Agent Zero powered exclusively by Google Gemini AI models (Flash 2.0 and 2.5).

##  Features

- ** Gemini AI Integration**: Uses Gemini 2.0 Flash and 1.5 Flash models exclusively
- ** Multi-Agent System**: Hierarchical agent architecture with superior-subordinate relationships
- ** Tool Execution**: Code execution, web browsing, memory management, and more
- ** Web Interface**: Real-time streaming UI with modern design
- ** Memory System**: Persistent memory and knowledge base
- ** MCP Support**: Model Context Protocol integration
- ** CLI Interface**: Command-line interface for terminal users

##  Requirements

- Python 3.8+
- Google Gemini API key
- Internet connection for API calls

##  Quick Start

### 1. Install Dependencies
`ash
pip install -r requirements.txt
`

### 2. Configure Environment
`ash
cp example.env .env
`
Edit .env and add your Gemini API key:
`env
GEMINI_API_KEY=your_gemini_api_key_here
`

### 3. Test Installation
`ash
python test_agent.py
`

### 4. Run Agent Zero

**Web Interface:**
`ash
python run_ui.py
`
Then open http://localhost:50001 in your browser

**CLI Interface:**
`ash
python run_cli.py
`

##  Configuration

### Gemini Models Used
- **Primary Chat**: gemini-2.0-flash-exp (1M context)
- **Utility**: gemini-1.5-flash (1M context)  
- **Browser**: gemini-2.0-flash-exp (1M context)
- **Embeddings**: 	ext-embedding-004

##  Architecture

### Core Components
`
agent-zero-gemini/
 agent.py                 # Core agent framework
 models.py               # Gemini AI integration
 run_ui.py              # Web interface launcher
 run_cli.py             # CLI launcher
 python/
    helpers/           # Utility functions
    tools/            # Agent tools
 prompts/default/      # System prompts
 webui/               # Web interface
 requirements.txt     # Dependencies
`

##  Available Tools

- **response**: Provide final response to user
- **codeexecutiontool**: Execute Python, bash, PowerShell code
- **memory tools**: Manage persistent memory
- **search_engine**: Web search capabilities
- **browser_agent**: Automated web browsing

##  Testing

Run the test suite:
`ash
python test_agent.py
`

##  Usage Examples

**Code Execution:**
`
User: Write a Python script to calculate fibonacci numbers
Agent: I'll create a Python script for fibonacci calculation...
`

**Research:**
`
User: Research the latest developments in AI
Agent: I'll search for recent AI developments...
`

##  Acknowledgments

- Original Agent Zero by frdel
- Google Gemini AI team
- LangChain community

---

**Ready to experience Agent Zero with Gemini AI? Start with python run_ui.py!** 
