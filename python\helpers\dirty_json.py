import json
import re
from typing import Any, Dict, Optional

class DirtyJson:
    """Utility class for parsing potentially malformed JSON"""

    @staticmethod
    def parse_string(text: str) -> Optional[Dict[str, Any]]:
        """Parse JSON from text, handling common formatting issues"""
        try:
            # First try standard JSON parsing
            return json.loads(text)
        except json.JSONDecodeError:
            pass

        # Try to extract <PERSON><PERSON><PERSON> from text
        json_pattern = r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}'
        matches = re.findall(json_pattern, text, re.DOTALL)

        for match in matches:
            try:
                return json.loads(match)
            except json.JSONDecodeError:
                continue

        return None

def stringify(obj: Any) -> str:
    """Convert object to JSON string"""
    try:
        return json.dumps(obj, indent=2, ensure_ascii=False)
    except (TypeError, ValueError):
        return str(obj)