import os
import importlib.util
import inspect
from typing import Any, Dict, List, Type
from python.helpers import files

class Tool:
    """Base class for all tools"""

    def __init__(self, agent: "Agent", name: str, args: Dict[str, Any]):
        self.agent = agent
        self.name = name
        self.args = args

    async def execute(self) -> str:
        """Execute the tool and return result"""
        raise NotImplementedError("Tool must implement execute method")

def load_tools() -> Dict[str, Type[Tool]]:
    """Load all tools from the tools directory"""
    tools = {}
    tools_dir = files.get_abs_path("python", "tools")

    if not os.path.exists(tools_dir):
        return tools

    for filename in os.listdir(tools_dir):
        if filename.endswith(".py") and not filename.startswith("__"):
            module_name = filename[:-3]  # Remove .py extension
            module_path = os.path.join(tools_dir, filename)

            try:
                spec = importlib.util.spec_from_file_location(module_name, module_path)
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)

                    # Look for Tool classes in the module
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and
                            issubclass(obj, Tool) and
                            obj != Tool):
                            tools[name.lower()] = obj
            except Exception as e:
                print(f"Error loading tool {module_name}: {e}")

    return tools

def extract_tool_calls(text: str) -> List[Dict[str, Any]]:
    """Extract tool calls from agent response text"""
    import re
    import json

    # Look for tool calls in various formats
    tool_calls = []

    # Pattern for JSON tool calls
    json_pattern = r'```json\s*(\{[^`]*\})\s*```'
    matches = re.findall(json_pattern, text, re.DOTALL)

    for match in matches:
        try:
            data = json.loads(match)
            if "tool_name" in data or "name" in data:
                tool_calls.append(data)
        except json.JSONDecodeError:
            continue

    # Pattern for function-style calls
    func_pattern = r'(\w+)\s*\(\s*([^)]*)\s*\)'
    func_matches = re.findall(func_pattern, text)

    for func_name, args_str in func_matches:
        if func_name.lower() in ["execute", "call", "run"]:
            continue
        try:
            # Simple argument parsing
            args = {}
            if args_str.strip():
                # Basic parsing - could be enhanced
                args["input"] = args_str.strip()
            tool_calls.append({
                "tool_name": func_name,
                "arguments": args
            })
        except:
            continue

    return tool_calls