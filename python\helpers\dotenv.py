import os
from typing import Optional
from dotenv import load_dotenv as _load_dotenv

def load_dotenv():
    """Load environment variables from .env file"""
    _load_dotenv()

def get_dotenv_value(key: str) -> Optional[str]:
    """Get environment variable value"""
    return os.getenv(key)

def set_dotenv_value(key: str, value: str):
    """Set environment variable value"""
    os.environ[key] = value