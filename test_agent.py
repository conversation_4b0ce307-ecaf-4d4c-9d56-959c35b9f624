#!/usr/bin/env python3

import os
import sys

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test that all imports work correctly"""
    print("Testing imports...")

    try:
        from python.helpers.dotenv import load_dotenv, get_dotenv_value
        print("✓ dotenv helper imported")

        from python.helpers.print_style import PrintStyle
        print("✓ print_style helper imported")

        from python.helpers import files, errors, history, tokens
        print("✓ core helpers imported")

        import models
        print("✓ models imported")

        from agent import Agent, AgentConfig, AgentContext, UserMessage
        print("✓ agent classes imported")

        from python.tools.response import Response
        print("✓ response tool imported")

        from python.tools.code_execution_tool import CodeExecutionTool
        print("✓ code execution tool imported")

        print("All imports successful!")
        return True

    except Exception as e:
        print(f"✗ Import error: {e}")
        return False

def test_basic_functionality():
    """Test basic agent functionality"""
    print("\nTesting basic functionality...")

    try:
        from python.helpers.dotenv import load_dotenv, get_dotenv_value
        import models
        from agent import AgentConfig, AgentContext, UserMessage

        # Load environment
        load_dotenv()

        # Create config
        config = AgentConfig(
            chat_model=models.ModelConfig(
                type=models.ModelType.CHAT,
                provider=models.ModelProvider.GEMINI,
                name="gemini-2.0-flash-exp",
                ctx_length=1000000
            ),
            utility_model=models.ModelConfig(
                type=models.ModelType.CHAT,
                provider=models.ModelProvider.GEMINI,
                name="gemini-1.5-flash",
                ctx_length=1000000
            ),
            embeddings_model=models.ModelConfig(
                type=models.ModelType.EMBEDDING,
                provider=models.ModelProvider.GEMINI,
                name="text-embedding-004"
            ),
            browser_model=models.ModelConfig(
                type=models.ModelType.CHAT,
                provider=models.ModelProvider.GEMINI,
                name="gemini-2.0-flash-exp",
                ctx_length=1000000
            ),
            mcp_servers="",
            prompts_subdir="",
            memory_subdir="",
            knowledge_subdirs=["default", "custom"]
        )
        print("✓ Agent config created")

        # Create context
        context = AgentContext(config=config)
        print("✓ Agent context created")

        # Test tool loading
        from python.helpers.extract_tools import load_tools
        tools = load_tools()
        print(f"✓ Loaded {len(tools)} tools: {list(tools.keys())}")

        print("Basic functionality test passed!")
        return True

    except Exception as e:
        print(f"✗ Basic functionality error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_prompts():
    """Test prompt loading"""
    print("\nTesting prompt system...")

    try:
        from python.helpers import files

        # Test prompt file reading
        prompt_path = files.get_abs_path("prompts", "default", "agent.system.main.md")
        if os.path.exists(prompt_path):
            content = files.read_file(prompt_path, agent_name="A0", agent_number=0)
            print(f"✓ Main prompt loaded ({len(content)} chars)")
        else:
            print("✗ Main prompt file not found")
            return False

        # Test other prompts
        for prompt_file in ["agent.system.main.role.md", "agent.system.tools.md"]:
            prompt_path = files.get_abs_path("prompts", "default", prompt_file)
            if os.path.exists(prompt_path):
                print(f"✓ {prompt_file} exists")
            else:
                print(f"✗ {prompt_file} missing")
                return False

        print("Prompt system test passed!")
        return True

    except Exception as e:
        print(f"✗ Prompt system error: {e}")
        return False

def main():
    """Run all tests"""
    print("Agent Zero - Gemini Edition Test Suite")
    print("=" * 50)

    tests = [
        test_imports,
        test_basic_functionality,
        test_prompts
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        if test():
            passed += 1

    print(f"\n{'=' * 50}")
    print(f"Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Agent Zero is ready to run.")
        print("\nTo start the web interface: python run_ui.py")
        print("To start the CLI: python run_cli.py")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == '__main__':
    main()