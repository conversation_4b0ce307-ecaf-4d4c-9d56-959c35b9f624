import os
import re
from pathlib import Path
from typing import Any, Dict, List, Optional

def get_base_dir() -> str:
    """Get the base directory of the project"""
    return os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def get_abs_path(*paths: str) -> str:
    """Get absolute path from base directory"""
    return os.path.join(get_base_dir(), *paths)

def read_file(file_path: str, _backup_dirs: List[str] = None, **kwargs) -> str:
    """Read file content with template variable substitution"""
    # Try main path first
    if os.path.exists(file_path):
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    else:
        # Try backup directories
        content = None
        if _backup_dirs:
            for backup_dir in _backup_dirs:
                backup_path = os.path.join(backup_dir, os.path.basename(file_path))
                if os.path.exists(backup_path):
                    with open(backup_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    break

        if content is None:
            raise FileNotFoundError(f"File not found: {file_path}")

    # Process template variables
    for key, value in kwargs.items():
        content = content.replace(f"{{{{{key}}}}}", str(value))

    return content

def parse_file(file_path: str, _backup_dirs: List[str] = None, **kwargs) -> Dict[str, Any]:
    """Parse file content and return as dictionary"""
    content = read_file(file_path, _backup_dirs, **kwargs)

    # Process includes
    include_pattern = r'\{\{\s*include\s+"([^"]+)"\s*\}\}'

    def replace_include(match):
        include_path = match.group(1)
        if not os.path.isabs(include_path):
            include_path = os.path.join(os.path.dirname(file_path), include_path)
        try:
            return read_file(include_path, _backup_dirs, **kwargs)
        except FileNotFoundError:
            return f"<!-- Include not found: {include_path} -->"

    content = re.sub(include_pattern, replace_include, content)

    # For now, return as simple content dict
    return {"content": content}

def remove_code_fences(content: str) -> str:
    """Remove markdown code fences from content"""
    # Remove ```language and ``` markers
    content = re.sub(r'^```\w*\n', '', content, flags=re.MULTILINE)
    content = re.sub(r'\n```$', '', content, flags=re.MULTILINE)
    return content

def ensure_dir(path: str):
    """Ensure directory exists"""
    os.makedirs(path, exist_ok=True)

def write_file(file_path: str, content: str):
    """Write content to file"""
    ensure_dir(os.path.dirname(file_path))
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)