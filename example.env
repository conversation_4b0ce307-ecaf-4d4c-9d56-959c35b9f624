﻿# Gemini AI API Configuration
GEMINI_API_KEY=your_gemini_api_key_here
GOOGLE_API_KEY=your_google_api_key_here

# Web UI Configuration
WEB_UI_HOST=localhost
WEB_UI_PORT=50001
FLASK_SECRET_KEY=your_secret_key_here

# Authentication (optional)
AUTH_LOGIN=
AUTH_PASSWORD=

# API Key for external access (optional)
API_KEY=

# Docker Configuration
DOCKER_ENABLED=false
DOCKER_NAME=A0-dev
DOCKER_IMAGE=frdel/agent-zero-run:development

# SSH Configuration
SSH_ENABLED=true
SSH_ADDR=localhost
SSH_PORT=55022
SSH_USER=root
SSH_PASS=

# Memory and Knowledge
MEMORY_SUBDIR=
KNOWLEDGE_SUBDIRS=default,custom

# MCP Servers
MCP_SERVERS=
