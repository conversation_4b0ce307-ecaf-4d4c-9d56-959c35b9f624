import sys
from typing import Optional

class PrintStyle:
    """Utility class for styled console output"""

    def __init__(
        self,
        font_color: Optional[str] = None,
        background_color: Optional[str] = None,
        bold: bool = False,
        italic: bool = False,
        padding: bool = True
    ):
        self.font_color = font_color
        self.background_color = background_color
        self.bold = bold
        self.italic = italic
        self.padding = padding

    def print(self, text: str):
        """Print text with styling"""
        if self.padding:
            print(f"\n{text}\n")
        else:
            print(text)

    def stream(self, text: str):
        """Stream text without newline"""
        print(text, end='', flush=True)

    @staticmethod
    def debug(text: str):
        """Print debug message"""
        print(f"[DEBUG] {text}")

    @staticmethod
    def info(text: str):
        """Print info message"""
        print(f"[INFO] {text}")

    @staticmethod
    def warning(text: str):
        """Print warning message"""
        print(f"[WARNING] {text}")

    @staticmethod
    def error(text: str):
        """Print error message"""
        print(f"[ERROR] {text}", file=sys.stderr)