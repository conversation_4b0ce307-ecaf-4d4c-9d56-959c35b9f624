import asyncio
import threading
from typing import Any, Callable, Coroutine

class DeferredTask:
    """Manages deferred async tasks in threads"""

    def __init__(self, thread_name: str = "DeferredTask"):
        self.thread_name = thread_name
        self.thread: threading.Thread | None = None
        self.loop: asyncio.AbstractEventLoop | None = None
        self.task: asyncio.Task | None = None
        self._result = None
        self._exception = None
        self._done = False

    def start_task(self, func: Callable[..., Coroutine[Any, Any, Any]], *args: Any, **kwargs: Any):
        """Start the async task in a new thread"""
        def run_in_thread():
            try:
                self.loop = asyncio.new_event_loop()
                asyncio.set_event_loop(self.loop)
                self.task = self.loop.create_task(func(*args, **kwargs))
                self._result = self.loop.run_until_complete(self.task)
                self._done = True
            except Exception as e:
                self._exception = e
                self._done = True
            finally:
                if self.loop:
                    self.loop.close()

        self.thread = threading.Thread(target=run_in_thread, name=self.thread_name)
        self.thread.start()

    def is_alive(self) -> bool:
        """Check if task is still running"""
        return self.thread is not None and self.thread.is_alive()

    def kill(self):
        """Kill the task"""
        if self.task and not self.task.done():
            self.task.cancel()
        if self.loop and self.loop.is_running():
            self.loop.stop()

    def result_sync(self) -> Any:
        """Get result synchronously (blocks until complete)"""
        if self.thread:
            self.thread.join()
        if self._exception:
            raise self._exception
        return self._result