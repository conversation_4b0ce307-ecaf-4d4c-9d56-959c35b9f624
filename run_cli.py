#!/usr/bin/env python3

import os
import sys
import asyncio

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python.helpers.dotenv import load_dotenv, get_dotenv_value
from python.helpers.print_style import PrintStyle
import models
from agent import Agent, AgentConfig, AgentContext, UserMessage

def create_agent_config():
    """Create agent configuration with Gemini models"""
    return AgentConfig(
        chat_model=models.ModelConfig(
            type=models.ModelType.CHAT,
            provider=models.ModelProvider.GEMINI,
            name="gemini-2.0-flash-exp",
            ctx_length=1000000
        ),
        utility_model=models.ModelConfig(
            type=models.ModelType.CHAT,
            provider=models.ModelProvider.GEMINI,
            name="gemini-1.5-flash",
            ctx_length=1000000
        ),
        embeddings_model=models.ModelConfig(
            type=models.ModelType.EMBEDDING,
            provider=models.ModelProvider.GEMINI,
            name="text-embedding-004"
        ),
        browser_model=models.ModelConfig(
            type=models.ModelType.CHAT,
            provider=models.ModelProvider.GEMINI,
            name="gemini-2.0-flash-exp",
            ctx_length=1000000
        ),
        mcp_servers=get_dotenv_value('MCP_SERVERS') or "",
        prompts_subdir=get_dotenv_value('PROMPTS_SUBDIR') or "",
        memory_subdir=get_dotenv_value('MEMORY_SUBDIR') or "",
        knowledge_subdirs=(get_dotenv_value('KNOWLEDGE_SUBDIRS') or "default,custom").split(','),
        code_exec_docker_enabled=get_dotenv_value('DOCKER_ENABLED') == 'true',
        code_exec_ssh_enabled=get_dotenv_value('SSH_ENABLED') == 'true',
        code_exec_ssh_addr=get_dotenv_value('SSH_ADDR') or 'localhost',
        code_exec_ssh_port=int(get_dotenv_value('SSH_PORT') or '55022'),
        code_exec_ssh_user=get_dotenv_value('SSH_USER') or 'root',
        code_exec_ssh_pass=get_dotenv_value('SSH_PASS') or ''
    )

def main():
    """Main CLI interface"""
    # Load environment variables
    load_dotenv()

    # Print startup information
    PrintStyle(font_color="green", padding=True).print("Agent Zero CLI - Gemini Edition")

    # Check for Gemini API key
    api_key = get_dotenv_value('GEMINI_API_KEY') or get_dotenv_value('GOOGLE_API_KEY')
    if not api_key or api_key == 'None':
        PrintStyle(font_color="red", padding=True).print(
            "ERROR: No Gemini API key found. Please set GEMINI_API_KEY in your .env file."
        )
        sys.exit(1)

    # Create agent context
    config = create_agent_config()
    context = AgentContext(config=config)

    PrintStyle(font_color="blue", padding=True).print("Agent Zero is ready! Type 'exit' to quit.")

    try:
        while True:
            # Get user input
            try:
                user_input = input("\n> ").strip()
            except (EOFError, KeyboardInterrupt):
                break

            if not user_input:
                continue

            if user_input.lower() in ['exit', 'quit', 'bye']:
                break

            if user_input.lower() == 'reset':
                context.reset()
                PrintStyle(font_color="green").print("Chat reset.")
                continue

            # Send message to agent
            try:
                user_msg = UserMessage(message=user_input)
                task = context.communicate(user_msg)

                # Get response
                response = task.result_sync()
                if response:
                    PrintStyle(font_color="green", padding=True).print(f"Agent: {response}")

            except Exception as e:
                PrintStyle(font_color="red", padding=True).print(f"Error: {str(e)}")

    except KeyboardInterrupt:
        pass

    PrintStyle(font_color="blue", padding=True).print("Goodbye!")

if __name__ == '__main__':
    main()