import subprocess
import tempfile
import os
from python.helpers.extract_tools import Tool
from python.helpers.print_style import PrintStyle
import python.helpers.log as Log

class CodeExecutionTool(Tool):
    """Tool for executing code in various languages"""

    async def execute(self) -> str:
        """Execute code and return result"""
        code = self.args.get("code", "")
        language = self.args.get("language", "python").lower()

        if not code:
            return "Error: No code provided"

        # Log code execution
        self.agent.context.log.log(
            type=Log.Type.TOOL,
            heading=f"Executing {language} code",
            content=code
        )

        PrintStyle(font_color="blue", padding=True).print(f"Executing {language} code:\n{code}")

        try:
            if language == "python":
                return await self._execute_python(code)
            elif language in ["bash", "shell", "sh"]:
                return await self._execute_bash(code)
            elif language == "powershell":
                return await self._execute_powershell(code)
            else:
                return f"Error: Unsupported language '{language}'"
        except Exception as e:
            error_msg = f"Code execution error: {str(e)}"
            PrintStyle(font_color="red").print(error_msg)
            return error_msg

    async def _execute_python(self, code: str) -> str:
        """Execute Python code"""
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
                f.write(code)
                temp_file = f.name

            # Execute Python code
            result = subprocess.run(
                ["python", temp_file],
                capture_output=True,
                text=True,
                timeout=30
            )

            # Clean up
            os.unlink(temp_file)

            output = result.stdout
            if result.stderr:
                output += f"\nSTDERR:\n{result.stderr}"

            return output or "Code executed successfully (no output)"

        except subprocess.TimeoutExpired:
            return "Error: Code execution timed out"
        except Exception as e:
            return f"Error executing Python code: {str(e)}"

    async def _execute_bash(self, code: str) -> str:
        """Execute bash code"""
        try:
            result = subprocess.run(
                code,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )

            output = result.stdout
            if result.stderr:
                output += f"\nSTDERR:\n{result.stderr}"

            return output or "Command executed successfully (no output)"

        except subprocess.TimeoutExpired:
            return "Error: Command execution timed out"
        except Exception as e:
            return f"Error executing bash command: {str(e)}"

    async def _execute_powershell(self, code: str) -> str:
        """Execute PowerShell code"""
        try:
            result = subprocess.run(
                ["powershell", "-Command", code],
                capture_output=True,
                text=True,
                timeout=30
            )

            output = result.stdout
            if result.stderr:
                output += f"\nSTDERR:\n{result.stderr}"

            return output or "PowerShell command executed successfully (no output)"

        except subprocess.TimeoutExpired:
            return "Error: PowerShell execution timed out"
        except Exception as e:
            return f"Error executing PowerShell: {str(e)}"