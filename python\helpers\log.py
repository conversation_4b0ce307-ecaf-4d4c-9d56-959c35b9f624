import uuid
from datetime import datetime
from typing import Any, Dict, List, Optional
from enum import Enum

class Type(Enum):
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    UTIL = "util"
    TOOL = "tool"

class ProgressUpdate:
    def __init__(self, message: str, progress: float = -1):
        self.message = message
        self.progress = progress

class LogItem:
    def __init__(
        self,
        type: Type,
        heading: Optional[str] = None,
        content: Optional[str] = None,
        kvps: Optional[Dict] = None,
        temp: bool = False,
        update_progress: Optional[ProgressUpdate] = None,
        id: Optional[str] = None,
        **kwargs
    ):
        self.id = id or str(uuid.uuid4())
        self.type = type
        self.heading = heading
        self.content = content
        self.kvps = kvps or {}
        self.temp = temp
        self.update_progress = update_progress
        self.timestamp = datetime.now()
        self.kwargs = kwargs

    def update(self, **kwargs):
        """Update log item properties"""
        for key, value in kwargs.items():
            setattr(self, key, value)

class Log:
    def __init__(self):
        self.guid = str(uuid.uuid4())
        self.logs: List[LogItem] = []
        self.updates: List[Dict] = []
        self.current_progress = ""

    def log(
        self,
        type: Type,
        heading: Optional[str] = None,
        content: Optional[str] = None,
        kvps: Optional[Dict] = None,
        temp: bool = False,
        update_progress: Optional[ProgressUpdate] = None,
        id: Optional[str] = None,
        **kwargs
    ) -> LogItem:
        """Add log entry"""
        item = LogItem(type, heading, content, kvps, temp, update_progress, id, **kwargs)
        self.logs.append(item)
        self.updates.append({"action": "add", "item": item})
        return item

    def set_progress(self, message: str, progress: float = -1):
        """Set current progress"""
        self.current_progress = message

    def reset(self):
        """Reset log"""
        self.logs.clear()
        self.updates.clear()
        self.current_progress = ""