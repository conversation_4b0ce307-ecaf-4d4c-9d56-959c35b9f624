﻿# Core dependencies for Agent Zero with Gemini AI
google-generativeai==0.8.3
google-cloud-aiplatform==1.71.1
google-auth==2.35.0
google-auth-oauthlib==1.2.1

# Web framework and UI
flask[async]==3.0.3
flask-basicauth==0.2.0
a2wsgi==1.10.8

# Agent framework core
nest-asyncio==1.6.0
asyncio==3.4.3
dataclasses-json==0.6.7
pydantic==2.9.2

# Tools and utilities
docker==7.1.0
paramiko==3.5.0
playwright==1.52.0
browser-use==0.2.5
duckduckgo-search==6.1.12

# Memory and knowledge
faiss-cpu==1.11.0
sentence-transformers==3.0.1
langchain-core==0.3.49
langchain-community==0.3.19
langchain-unstructured[all-docs]==0.1.6

# Document processing
pypdf==4.3.1
unstructured[all-docs]==0.16.23
unstructured-client==0.31.0
newspaper3k==0.2.8
lxml_html_clean==0.3.1
pymupdf==1.25.3
pytesseract==0.3.13
pdf2image==1.17.0

# Audio processing
openai-whisper==20240930

# Utilities
python-dotenv==1.1.0
tiktoken==0.8.0
markdown==3.7
markdownify==1.1.0
webcolors==24.6.0
pytz==2024.2
GitPython==3.1.43
inputimeout==1.0.4
crontab==1.0.1
pathspec>=0.12.1
psutil>=7.0.0

# MCP support
fastmcp==2.3.4
mcp==1.9.0
