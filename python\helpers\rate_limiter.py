import asyncio
import time
from typing import Dict, Callable, Awaitable, Optional
from dataclasses import dataclass, field

@dataclass
class RateLimiter:
    """Rate limiter for API calls"""
    seconds: int = 60
    limits: Dict[str, int] = field(default_factory=dict)
    usage: Dict[str, int] = field(default_factory=dict)
    reset_time: float = field(default_factory=time.time)

    def __post_init__(self):
        self.reset_time = time.time() + self.seconds

    def add(self, **kwargs):
        """Add usage to the rate limiter"""
        current_time = time.time()

        # Reset if time window has passed
        if current_time >= self.reset_time:
            self.usage.clear()
            self.reset_time = current_time + self.seconds

        # Add usage
        for key, value in kwargs.items():
            if key in self.limits and self.limits[key] > 0:
                self.usage[key] = self.usage.get(key, 0) + value

    def is_limited(self) -> bool:
        """Check if any limit is exceeded"""
        for key, limit in self.limits.items():
            if limit > 0 and self.usage.get(key, 0) >= limit:
                return True
        return False

    def time_until_reset(self) -> float:
        """Time until rate limit resets"""
        return max(0, self.reset_time - time.time())

    async def wait(self, callback: Optional[Callable[[str, str, int, int], Awaitable[None]]] = None):
        """Wait if rate limited"""
        while self.is_limited():
            wait_time = self.time_until_reset()
            if wait_time > 0:
                if callback:
                    for key, limit in self.limits.items():
                        if limit > 0 and self.usage.get(key, 0) >= limit:
                            await callback(
                                f"Rate limited for {key}",
                                key,
                                self.usage.get(key, 0),
                                limit
                            )
                await asyncio.sleep(min(wait_time, 1))  # Check every second
            else:
                break