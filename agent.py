import asyncio
import nest_asyncio
nest_asyncio.apply()

import os
from collections import OrderedDict
from dataclasses import dataclass, field
from datetime import datetime, timezone
from typing import Any, Awaitable, Coroutine, Dict, List, Tuple, Union, Callable
from enum import Enum
import uuid
import json

import models
from python.helpers import extract_tools, files, errors, history, tokens
from python.helpers import dirty_json
from python.helpers.print_style import PrintStyle
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_core.messages import HumanMessage, SystemMessage, BaseMessage
import python.helpers.log as Log
from python.helpers.dirty_json import DirtyJson
from python.helpers.defer import DeferredTask
from python.helpers.localization import Localization

class AgentContextType(Enum):
    USER = "user"
    TASK = "task"
    MCP = "mcp"

class AgentContext:
    _contexts: dict[str, "AgentContext"] = {}
    _counter: int = 0

    def __init__(
        self,
        config: "AgentConfig",
        id: str | None = None,
        name: str | None = None,
        agent0: "Agent|None" = None,
        log: Log.Log | None = None,
        paused: bool = False,
        streaming_agent: "Agent|None" = None,
        created_at: datetime | None = None,
        type: AgentContextType = AgentContextType.USER,
        last_message: datetime | None = None,
    ):
        # build context
        self.id = id or str(uuid.uuid4())
        self.name = name
        self.config = config
        self.log = log or Log.Log()
        self.agent0 = agent0 or Agent(0, self.config, self)
        self.paused = paused
        self.streaming_agent = streaming_agent
        self.task: DeferredTask | None = None
        self.created_at = created_at or datetime.now(timezone.utc)
        self.type = type
        AgentContext._counter += 1
        self.no = AgentContext._counter

        # set to start of unix epoch
        self.last_message = last_message or datetime.now(timezone.utc)

        existing = self._contexts.get(self.id, None)
        if existing:
            AgentContext.remove(self.id)
        self._contexts[self.id] = self

    @staticmethod
    def get(id: str):
        return AgentContext._contexts.get(id, None)

    @staticmethod
    def first():
        if not AgentContext._contexts:
            return None
        return list(AgentContext._contexts.values())[0]

    @staticmethod
    def all():
        return list(AgentContext._contexts.values())

    @staticmethod
    def remove(id: str):
        context = AgentContext._contexts.pop(id, None)
        if context and context.task:
            context.task.kill()
        return context

    def serialize(self):
        return {
            "id": self.id,
            "name": self.name,
            "created_at": (
                Localization.get().serialize_datetime(self.created_at)
                if self.created_at
                else Localization.get().serialize_datetime(datetime.fromtimestamp(0))
            ),
            "no": self.no,
            "log_guid": self.log.guid,
            "log_version": len(self.log.updates),
            "log_length": len(self.log.logs),
            "paused": self.paused,
            "last_message": (
                Localization.get().serialize_datetime(self.last_message)
                if self.last_message
                else Localization.get().serialize_datetime(datetime.fromtimestamp(0))
            ),
            "type": self.type.value,
        }

    @staticmethod
    def log_to_all(
        type: Log.Type,
        heading: str | None = None,
        content: str | None = None,
        kvps: dict | None = None,
        temp: bool | None = None,
        update_progress: Log.ProgressUpdate | None = None,
        id: str | None = None,
        **kwargs,
    ) -> list[Log.LogItem]:
        items: list[Log.LogItem] = []
        for context in AgentContext.all():
            items.append(
                context.log.log(
                    type, heading, content, kvps, temp, update_progress, id, **kwargs
                )
            )
        return items

    def kill_process(self):
        if self.task:
            self.task.kill()

    def reset(self):
        self.kill_process()
        self.log.reset()
        self.agent0 = Agent(0, self.config, self)
        self.streaming_agent = None
        self.paused = False

    def nudge(self):
        self.kill_process()
        self.paused = False
        self.task = self.run_task(self.get_agent().monologue)
        return self.task

    def get_agent(self):
        return self.streaming_agent or self.agent0

    def communicate(self, msg: "UserMessage", broadcast_level: int = 1):
        self.paused = False  # unpause if paused
        current_agent = self.get_agent()
        if self.task and self.task.is_alive():
            # set intervention messages to agent(s):
            intervention_agent = current_agent
            while intervention_agent and broadcast_level != 0:
                intervention_agent.intervention = msg
                broadcast_level -= 1
                intervention_agent = intervention_agent.data.get(
                    Agent.DATA_NAME_SUPERIOR, None
                )
        else:
            self.task = self.run_task(self._process_chain, current_agent, msg)
        return self.task

    def run_task(
        self, func: Callable[..., Coroutine[Any, Any, Any]], *args: Any, **kwargs: Any
    ):
        if not self.task:
            self.task = DeferredTask(
                thread_name=self.__class__.__name__,
            )
        self.task.start_task(func, *args, **kwargs)
        return self.task

    # this wrapper ensures that superior agents are called back if the chat was loaded from file and original callstack is gone
    async def _process_chain(self, agent: "Agent", msg: "UserMessage|str", user=True):
        try:
            msg_template = (
                agent.hist_add_user_message(msg)  # type: ignore
                if user
                else agent.hist_add_tool_result(
                    tool_name="call_subordinate", tool_result=msg  # type: ignore
                )
            )
            response = await agent.monologue()  # type: ignore
            superior = agent.data.get(Agent.DATA_NAME_SUPERIOR, None)
            if superior:
                response = await self._process_chain(superior, response, False)  # type: ignore
            return response
        except Exception as e:
            agent.handle_critical_exception(e)

@dataclass
class AgentConfig:
    chat_model: models.ModelConfig
    utility_model: models.ModelConfig
    embeddings_model: models.ModelConfig
    browser_model: models.ModelConfig
    mcp_servers: str
    prompts_subdir: str = ""
    memory_subdir: str = ""
    knowledge_subdirs: list[str] = field(default_factory=lambda: ["default", "custom"])
    code_exec_docker_enabled: bool = False
    code_exec_docker_name: str = "A0-dev"
    code_exec_docker_image: str = "frdel/agent-zero-run:development"
    code_exec_docker_ports: dict[str, int] = field(
        default_factory=lambda: {"22/tcp": 55022, "80/tcp": 55080}
    )
    code_exec_docker_volumes: dict[str, dict[str, str]] = field(
        default_factory=lambda: {
            files.get_base_dir(): {"bind": "/a0", "mode": "rw"},
            files.get_abs_path("work_dir"): {"bind": "/root", "mode": "rw"},
        }
    )
    code_exec_ssh_enabled: bool = True
    code_exec_ssh_addr: str = "localhost"
    code_exec_ssh_port: int = 55022
    code_exec_ssh_user: str = "root"
    code_exec_ssh_pass: str = ""
    additional: Dict[str, Any] = field(default_factory=dict)

@dataclass
class UserMessage:
    message: str
    attachments: list[str] = field(default_factory=list[str])
    system_message: list[str] = field(default_factory=list[str])

class LoopData:
    def __init__(self, **kwargs):
        self.iteration = -1
        self.system = []
        self.user_message: history.Message | None = None
        self.history_output: list[history.OutputMessage] = []
        self.extras_temporary: OrderedDict[str, history.MessageContent] = OrderedDict()
        self.extras_persistent: OrderedDict[str, history.MessageContent] = OrderedDict()
        self.last_response = ""
        self.params_temporary: dict = {}
        self.params_persistent: dict = {}
        # override values with kwargs
        for key, value in kwargs.items():
            setattr(self, key, value)

# intervention exception class - skips rest of message loop iteration
class InterventionException(Exception):
    pass

# killer exception class - not forwarded to LLM, cannot be fixed on its own, ends message loop
class RepairableException(Exception):
    pass

class HandledException(Exception):
    pass

class Agent:
    DATA_NAME_SUPERIOR = "_superior"
    DATA_NAME_SUBORDINATE = "_subordinate"
    DATA_NAME_CTX_WINDOW = "ctx_window"

    def __init__(
        self, number: int, config: AgentConfig, context: AgentContext | None = None
    ):
        # agent config
        self.config = config
        # agent context
        self.context = context or AgentContext(config=config, agent0=self)
        # non-config vars
        self.number = number
        self.agent_name = f"A{self.number}"
        self.history = history.History(self)
        self.last_user_message: history.Message | None = None
        self.intervention: UserMessage | None = None
        self.data = {}  # free data object all the tools can use

    async def monologue(self):
        while True:
            try:
                # loop data dictionary to pass to extensions
                self.loop_data = LoopData(user_message=self.last_user_message)
                # call monologue_start extensions
                await self.call_extensions("monologue_start", loop_data=self.loop_data)
                printer = PrintStyle(italic=True, font_color="#b3ffd9", padding=False)

                # let the agent run message loop until he stops it with a response tool
                while True:
                    self.context.streaming_agent = self  # mark self as current streamer
                    self.loop_data.iteration += 1
                    self.loop_data.params_temporary = {}  # clear temporary params

                    # call message_loop_start extensions
                    await self.call_extensions(
                        "message_loop_start", loop_data=self.loop_data
                    )

                    try:
                        # prepare LLM chain (model, system, history)
                        prompt = await self.prepare_prompt(loop_data=self.loop_data)

                        # call before_main_llm_call extensions
                        await self.call_extensions("before_main_llm_call", loop_data=self.loop_data)

                        async def reasoning_callback(chunk: str, full: str):
                            if chunk == full:
                                printer.print("Reasoning: ")  # start of reasoning
                            printer.stream(chunk)
                            await self.handle_reasoning_stream(full)

                        async def stream_callback(chunk: str, full: str):
                            # output the agent response stream
                            if chunk == full:
                                printer.print("Response: ")  # start of response
                            printer.stream(chunk)
                            await self.handle_response_stream(full)

                        # call main LLM
                        agent_response, _reasoning = await self.call_chat_model(
                            messages=prompt,
                            response_callback=stream_callback,
                            reasoning_callback=reasoning_callback,
                        )

                        await self.handle_intervention(agent_response)

                        if self.loop_data.last_response == agent_response:
                            # if assistant_response is the same as last message in history, let him know
                            # Append the assistant's response to the history
                            self.hist_add_ai_response(agent_response)
                            # Append warning message to the history
                            warning_msg = self.read_prompt("fw.msg_repeat.md")
                            self.hist_add_warning(message=warning_msg)
                            PrintStyle(font_color="orange", padding=True).print(warning_msg)
                            self.context.log.log(type="warning", content=warning_msg)
                        else:
                            # otherwise proceed with tool
                            # Append the assistant's response to the history
                            self.hist_add_ai_response(agent_response)
                            # process tools requested in agent message
                            tools_result = await self.process_tools(agent_response)
                            if tools_result:
                                # final response of message loop available
                                return tools_result  # break the execution if the task is done

                    # exceptions inside message loop:
                    except InterventionException as e:
                        pass  # intervention message has been handled in handle_intervention(), proceed with conversation loop
                    except RepairableException as e:
                        # Forward repairable errors to the LLM, maybe it can fix them
                        error_message = errors.format_error(e)
                        self.hist_add_warning(error_message)
                        PrintStyle(font_color="red", padding=True).print(error_message)
                        self.context.log.log(type="error", content=error_message)
                    except Exception as e:
                        # Other exception kill the loop
                        self.handle_critical_exception(e)
                    finally:
                        # call message_loop_end extensions
                        await self.call_extensions(
                            "message_loop_end", loop_data=self.loop_data
                        )

            # exceptions outside message loop:
            except InterventionException as e:
                pass  # just start over
            except Exception as e:
                self.handle_critical_exception(e)
            finally:
                self.context.streaming_agent = None  # unset current streamer
                # call monologue_end extensions
                await self.call_extensions("monologue_end", loop_data=self.loop_data)  # type: ignore

    async def prepare_prompt(self, loop_data: LoopData) -> list[BaseMessage]:
        self.context.log.set_progress("Building prompt")
        # call extensions before setting prompts
        await self.call_extensions("message_loop_prompts_before", loop_data=loop_data)
        # set system prompt and message history
        loop_data.system = await self.get_system_prompt(self.loop_data)
        loop_data.history_output = self.history.output()
        # and allow extensions to edit them
        await self.call_extensions("message_loop_prompts_after", loop_data=loop_data)
        # concatenate system prompt
        system_text = "\n\n".join(loop_data.system)
        # join extras
        extras = history.Message(
            False,
            content=self.read_prompt(
                "agent.context.extras.md",
                extras=dirty_json.stringify(
                    {**loop_data.extras_persistent, **loop_data.extras_temporary}
                ),
            ),
        ).output()
        loop_data.extras_temporary.clear()
        # convert history + extras to LLM format
        history_langchain: list[BaseMessage] = history.output_langchain(
            loop_data.history_output + extras
        )
        # build full prompt from system prompt, message history and extras
        full_prompt: list[BaseMessage] = [
            SystemMessage(content=system_text),
            *history_langchain,
        ]
        full_text = ChatPromptTemplate.from_messages(full_prompt).format()
        # store as last context window content
        self.set_data(
            Agent.DATA_NAME_CTX_WINDOW,
            {
                "text": full_text,
                "tokens": tokens.approximate_tokens(full_text),
            },
        )
        return full_prompt

    async def get_system_prompt(self, loop_data: LoopData) -> List[str]:
        """Get system prompt for the agent"""
        prompts = []

        # Main system prompt
        main_prompt = self.read_prompt("agent.system.main.md",
                                     agent_name=self.agent_name,
                                     agent_number=self.number)
        prompts.append(main_prompt)

        # Role prompt
        role_prompt = self.read_prompt("agent.system.main.role.md")
        prompts.append(role_prompt)

        # Tools prompt
        tools_prompt = self.read_prompt("agent.system.tools.md")
        prompts.append(tools_prompt)

        return prompts

    def read_prompt(self, filename: str, **kwargs) -> str:
        """Read prompt file with variable substitution"""
        prompts_dir = files.get_abs_path("prompts", self.config.prompts_subdir or "default")
        backup_dirs = [files.get_abs_path("prompts", "default")] if self.config.prompts_subdir else []

        file_path = os.path.join(prompts_dir, filename)
        return files.read_file(file_path, backup_dirs, **kwargs)

    async def call_chat_model(
        self,
        messages: List[BaseMessage],
        response_callback: Callable[[str, str], Awaitable[None]] | None = None,
        reasoning_callback: Callable[[str, str], Awaitable[None]] | None = None,
    ) -> Tuple[str, str]:
        """Call the chat model with messages"""
        model = models.get_chat_model(
            models.ModelProvider.GEMINI,
            self.config.chat_model.name,
            **self.config.chat_model.build_kwargs()
        )

        return await model.unified_call(
            messages=messages,
            response_callback=response_callback,
            reasoning_callback=reasoning_callback
        )

    async def process_tools(self, agent_response: str) -> str | None:
        """Process tool calls in agent response"""
        tool_calls = extract_tools.extract_tool_calls(agent_response)

        if not tool_calls:
            return None

        # Load available tools
        available_tools = extract_tools.load_tools()

        for tool_call in tool_calls:
            tool_name = tool_call.get("tool_name", tool_call.get("name", "")).lower()
            tool_args = tool_call.get("arguments", tool_call.get("args", {}))

            if tool_name in available_tools:
                try:
                    tool_class = available_tools[tool_name]
                    tool_instance = tool_class(self, tool_name, tool_args)
                    result = await tool_instance.execute()

                    # Add tool result to history
                    self.hist_add_tool_result(tool_name, result)

                    # Check if this is a response tool (ends conversation)
                    if tool_name == "response":
                        return result

                except Exception as e:
                    error_msg = f"Error executing tool {tool_name}: {str(e)}"
                    self.hist_add_warning(error_msg)
                    PrintStyle(font_color="red").print(error_msg)
            else:
                error_msg = f"Unknown tool: {tool_name}"
                self.hist_add_warning(error_msg)
                PrintStyle(font_color="orange").print(error_msg)

        return None

    def hist_add_user_message(self, message: Union[str, UserMessage]) -> history.Message:
        """Add user message to history"""
        if isinstance(message, str):
            content = message
        else:
            content = message.message

        self.last_user_message = self.history.add_user_message(content)
        return self.last_user_message

    def hist_add_ai_response(self, response: str) -> history.Message:
        """Add AI response to history"""
        return self.history.add_ai_message(response)

    def hist_add_tool_result(self, tool_name: str, result: str) -> history.Message:
        """Add tool result to history"""
        return self.history.add_tool_result(tool_name, result)

    def hist_add_warning(self, message: str) -> history.Message:
        """Add warning to history"""
        return self.history.add_warning(message)

    async def handle_intervention(self, agent_response: str):
        """Handle user intervention during agent execution"""
        if self.intervention:
            intervention_msg = self.intervention
            self.intervention = None  # Clear intervention

            # Add intervention to history
            self.hist_add_user_message(intervention_msg)

            # Log intervention
            self.context.log.log(
                type=Log.Type.INFO,
                heading="User Intervention",
                content=intervention_msg.message if hasattr(intervention_msg, 'message') else str(intervention_msg)
            )

            raise InterventionException("User intervention received")

    async def handle_response_stream(self, full_response: str):
        """Handle streaming response from model"""
        self.loop_data.last_response = full_response

        # Log to context
        self.context.log.log(
            type=Log.Type.INFO,
            heading="Agent Response",
            content=full_response,
            temp=True
        )

    async def handle_reasoning_stream(self, full_reasoning: str):
        """Handle streaming reasoning from model"""
        # Log reasoning if available
        if full_reasoning:
            self.context.log.log(
                type=Log.Type.INFO,
                heading="Agent Reasoning",
                content=full_reasoning,
                temp=True
            )

    def handle_critical_exception(self, exception: Exception):
        """Handle critical exceptions that end the conversation"""
        error_message = errors.format_error(exception)

        # Log error
        self.context.log.log(
            type=Log.Type.ERROR,
            heading="Critical Error",
            content=error_message
        )

        # Print error
        PrintStyle(font_color="red", padding=True).print(f"Critical error in {self.agent_name}: {error_message}")

        # Add to history
        self.hist_add_warning(f"Critical error occurred: {str(exception)}")

        # Mark context as paused
        self.context.paused = True

    async def call_extensions(self, hook_name: str, **kwargs):
        """Call extension hooks"""
        # Extension system placeholder - can be implemented later
        pass

    def set_data(self, key: str, value: Any):
        """Set data in agent's data store"""
        self.data[key] = value

    def get_data(self, key: str, default: Any = None) -> Any:
        """Get data from agent's data store"""
        return self.data.get(key, default)

    def create_subordinate(self, number: int) -> "Agent":
        """Create a subordinate agent"""
        subordinate = Agent(number, self.config, self.context)
        subordinate.data[Agent.DATA_NAME_SUPERIOR] = self
        self.data[Agent.DATA_NAME_SUBORDINATE] = subordinate
        return subordinate