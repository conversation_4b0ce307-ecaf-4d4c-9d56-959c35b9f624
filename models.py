from dataclasses import dataclass, field
from enum import Enum
import logging
import os
from typing import (
    Any, Awaitable, Callable, List, Optional, Iterator, AsyncItera<PERSON>,
    <PERSON><PERSON>, TypedDict, Dict
)
import asyncio
import json
import time
from datetime import datetime, timedelta

import google.generativeai as genai
from google.generativeai.types import HarmCategory, HarmBlockThreshold
from google.ai.generativelanguage_v1beta.types import content

from python.helpers import dotenv
from python.helpers.dotenv import load_dotenv
from python.helpers.rate_limiter import RateLimiter
from python.helpers.tokens import approximate_tokens
from langchain_core.language_models.chat_models import SimpleChatModel
from langchain_core.outputs.chat_generation import ChatGenerationChunk
from langchain_core.callbacks.manager import (
    CallbackManagerForLLMRun, AsyncCallbackManagerForLLMRun,
)
from langchain_core.messages import (
    BaseMessage, AIMessageChunk, HumanMessage, SystemMessage,
)
from langchain.embeddings.base import Embeddings
from sentence_transformers import SentenceTransformer

# Initialize
load_dotenv()

class ModelType(Enum):
    CHAT = "Chat"
    EMBEDDING = "Embedding"

class ModelProvider(Enum):
    GEMINI = "Google Gemini"

@dataclass
class ModelConfig:
    type: ModelType
    provider: ModelProvider
    name: str
    api_base: str = ""
    ctx_length: int = 0
    limit_requests: int = 0
    limit_input: int = 0
    limit_output: int = 0
    vision: bool = False
    kwargs: dict = field(default_factory=dict)

    def build_kwargs(self):
        kwargs = self.kwargs.copy() or {}
        if self.api_base and "api_base" not in kwargs:
            kwargs["api_base"] = self.api_base
        return kwargs

class ChatChunk(TypedDict):
    """Simplified response chunk for chat models."""
    response_delta: str
    reasoning_delta: str

rate_limiters: dict[str, RateLimiter] = {}

def get_api_key(service: str) -> str:
    return (
        dotenv.get_dotenv_value(f"API_KEY_{service.upper()}") or
        dotenv.get_dotenv_value(f"{service.upper()}_API_KEY") or
        dotenv.get_dotenv_value(f"{service.upper()}_API_TOKEN") or
        dotenv.get_dotenv_value("GEMINI_API_KEY") or
        dotenv.get_dotenv_value("GOOGLE_API_KEY") or
        "None"
    )

def get_rate_limiter(
    provider: ModelProvider, name: str, requests: int, input: int, output: int
) -> RateLimiter:
    key = f"{provider.name}\\{name}"
    rate_limiters[key] = limiter = rate_limiters.get(key, RateLimiter(seconds=60))
    limiter.limits["requests"] = requests or 0
    limiter.limits["input"] = input or 0
    limiter.limits["output"] = output or 0
    return limiter

class GeminiChatWrapper(SimpleChatModel):
    model_name: str
    provider: str
    kwargs: dict = {}

    def __init__(self, model: str, provider: str, **kwargs: Any):
        super().__init__(model_name=model, provider=provider, kwargs=kwargs)

        # Configure Gemini API
        api_key = get_api_key("GEMINI")
        if api_key and api_key != "None":
            genai.configure(api_key=api_key)

        # Initialize the model
        self.model = genai.GenerativeModel(
            model_name=model,
            safety_settings={
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_NONE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_NONE,
            }
        )

    @property
    def _llm_type(self) -> str:
        return "gemini-chat"

    def _convert_messages(self, messages: List[BaseMessage]) -> Tuple[List[dict], Optional[str]]:
        result = []
        system_instruction = None

        for m in messages:
            if m.type == "system":
                system_instruction = m.content
                continue

            role = "user" if m.type == "human" else "model"
            message_dict = {"role": role, "parts": [{"text": m.content}]}

            # Handle tool calls for AI messages
            tool_calls = getattr(m, "tool_calls", None)
            if tool_calls:
                for tool_call in tool_calls:
                    message_dict["parts"].append({
                        "function_call": {
                            "name": tool_call["name"],
                            "args": tool_call["args"]
                        }
                    })

            result.append(message_dict)

        return result, system_instruction

    def _call(
        self, messages: List[BaseMessage], stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None, **kwargs: Any,
    ) -> str:
        msgs, system_instruction = self._convert_messages(messages)

        # Update model with system instruction if provided
        if system_instruction:
            model = genai.GenerativeModel(
                model_name=self.model_name,
                system_instruction=system_instruction,
                safety_settings=self.model._safety_settings
            )
        else:
            model = self.model

        response = model.generate_content(
            msgs,
            generation_config=genai.types.GenerationConfig(
                stop_sequences=stop,
                **{**self.kwargs, **kwargs}
            )
        )

        return response.text if response.text else ""

    def _stream(
        self, messages: List[BaseMessage], stop: Optional[List[str]] = None,
        run_manager: Optional[CallbackManagerForLLMRun] = None, **kwargs: Any,
    ) -> Iterator[ChatGenerationChunk]:
        msgs, system_instruction = self._convert_messages(messages)

        # Update model with system instruction if provided
        if system_instruction:
            model = genai.GenerativeModel(
                model_name=self.model_name,
                system_instruction=system_instruction,
                safety_settings=self.model._safety_settings
            )
        else:
            model = self.model

        response = model.generate_content(
            msgs,
            generation_config=genai.types.GenerationConfig(
                stop_sequences=stop,
                **{**self.kwargs, **kwargs}
            ),
            stream=True
        )

        for chunk in response:
            if chunk.text:
                yield ChatGenerationChunk(
                    message=AIMessageChunk(content=chunk.text)
                )

    async def _astream(
        self, messages: List[BaseMessage], stop: Optional[List[str]] = None,
        run_manager: Optional[AsyncCallbackManagerForLLMRun] = None, **kwargs: Any,
    ) -> AsyncIterator[ChatGenerationChunk]:
        msgs, system_instruction = self._convert_messages(messages)

        # Update model with system instruction if provided
        if system_instruction:
            model = genai.GenerativeModel(
                model_name=self.model_name,
                system_instruction=system_instruction,
                safety_settings=self.model._safety_settings
            )
        else:
            model = self.model

        response = await model.generate_content_async(
            msgs,
            generation_config=genai.types.GenerationConfig(
                stop_sequences=stop,
                **{**self.kwargs, **kwargs}
            ),
            stream=True
        )

        async for chunk in response:
            if chunk.text:
                yield ChatGenerationChunk(
                    message=AIMessageChunk(content=chunk.text)
                )

    async def unified_call(
        self,
        system_message="",
        user_message="",
        messages: List[BaseMessage] | None = None,
        response_callback: Callable[[str, str], Awaitable[None]] | None = None,
        reasoning_callback: Callable[[str, str], Awaitable[None]] | None = None,
        tokens_callback: Callable[[str, int], Awaitable[None]] | None = None,
        **kwargs: Any,
    ) -> Tuple[str, str]:
        if not messages:
            messages = []

        # Construct messages
        if system_message:
            messages.insert(0, SystemMessage(content=system_message))
        if user_message:
            messages.append(HumanMessage(content=user_message))

        msgs, system_instruction = self._convert_messages(messages)

        # Update model with system instruction if provided
        if system_instruction:
            model = genai.GenerativeModel(
                model_name=self.model_name,
                system_instruction=system_instruction,
                safety_settings=self.model._safety_settings
            )
        else:
            model = self.model

        # Call model with streaming
        response = await model.generate_content_async(
            msgs,
            generation_config=genai.types.GenerationConfig(**{**self.kwargs, **kwargs}),
            stream=True
        )

        # Results
        reasoning = ""  # Gemini doesn't separate reasoning from response
        full_response = ""

        # Iterate over chunks
        async for chunk in response:
            if chunk.text:
                full_response += chunk.text

                if response_callback:
                    await response_callback(chunk.text, full_response)
                if tokens_callback:
                    await tokens_callback(
                        chunk.text, approximate_tokens(chunk.text)
                    )

        return full_response, reasoning

class BrowserCompatibleChatWrapper(GeminiChatWrapper):
    """A wrapper for browser agent that can filter/sanitize messages before sending them to the LLM."""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)

class GeminiEmbeddingWrapper(Embeddings):
    model_name: str
    kwargs: dict = {}

    def __init__(self, model: str, provider: str, **kwargs: Any):
        self.model_name = model
        self.kwargs = kwargs

        # Configure Gemini API
        api_key = get_api_key("GEMINI")
        if api_key and api_key != "None":
            genai.configure(api_key=api_key)

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        embeddings = []
        for text in texts:
            result = genai.embed_content(
                model=self.model_name,
                content=text,
                task_type="retrieval_document"
            )
            embeddings.append(result['embedding'])
        return embeddings

    def embed_query(self, text: str) -> List[float]:
        result = genai.embed_content(
            model=self.model_name,
            content=text,
            task_type="retrieval_query"
        )
        return result['embedding']

class LocalSentenceTransformerWrapper(Embeddings):
    """Local wrapper for sentence-transformers models to avoid API calls"""

    def __init__(self, provider: str, model: str, **kwargs: Any):
        # Remove the "sentence-transformers/" prefix if present
        if model.startswith("sentence-transformers/"):
            model = model[len("sentence-transformers/"):]
        self.model = SentenceTransformer(model, **kwargs)
        self.model_name = model

    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        embeddings = self.model.encode(texts, convert_to_tensor=False)
        return embeddings.tolist() if hasattr(embeddings, "tolist") else embeddings

    def embed_query(self, text: str) -> List[float]:
        embedding = self.model.encode([text], convert_to_tensor=False)
        result = (
            embedding[0].tolist() if hasattr(embedding[0], "tolist") else embedding[0]
        )
        return result

def get_model(type: ModelType, provider: ModelProvider, name: str, **kwargs: Any):
    if type == ModelType.CHAT:
        return GeminiChatWrapper(name, provider.name.lower(), **kwargs)
    elif type == ModelType.EMBEDDING:
        if name.startswith("sentence-transformers/"):
            return LocalSentenceTransformerWrapper(provider.name.lower(), name, **kwargs)
        return GeminiEmbeddingWrapper(name, provider.name.lower(), **kwargs)
    else:
        raise ValueError(f"Unsupported model type: {type}")

def get_chat_model(provider: ModelProvider, name: str, **kwargs: Any) -> GeminiChatWrapper:
    return GeminiChatWrapper(name, provider.name.lower(), **kwargs)

def get_browser_model(provider: ModelProvider, name: str, **kwargs: Any) -> BrowserCompatibleChatWrapper:
    return BrowserCompatibleChatWrapper(name, provider.name.lower(), **kwargs)

def get_embedding_model(provider: ModelProvider, name: str, **kwargs: Any) -> GeminiEmbeddingWrapper | LocalSentenceTransformerWrapper:
    if name.startswith("sentence-transformers/"):
        return LocalSentenceTransformerWrapper(provider.name.lower(), name, **kwargs)
    return GeminiEmbeddingWrapper(name, provider.name.lower(), **kwargs)