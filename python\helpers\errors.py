import traceback
from typing import Any

def error_text(exception: Exception) -> str:
    """Get error text from exception"""
    return str(exception)

def format_error(exception: Exception) -> str:
    """Format error with traceback"""
    return f"Error: {str(exception)}\n\nTraceback:\n{traceback.format_exc()}"

def handle_error(exception: Exception, context: str = "") -> str:
    """Handle and format error with context"""
    error_msg = f"Error in {context}: {str(exception)}" if context else f"Error: {str(exception)}"
    return error_msg