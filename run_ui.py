#!/usr/bin/env python3

import os
import sys
import asyncio
from flask import Flask, render_template, request, jsonify, session
from flask_basicauth import BasicAuth
import json
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from python.helpers.dotenv import load_dotenv, get_dotenv_value
from python.helpers.print_style import PrintStyle
import models
from agent import Agent, AgentConfig, AgentContext, UserMessage

# Load environment variables
load_dotenv()

# Initialize Flask app
app = Flask(__name__, template_folder='webui', static_folder='webui/static')
app.secret_key = get_dotenv_value('FLASK_SECRET_KEY') or 'your-secret-key-here'

# Basic authentication if configured
auth_login = get_dotenv_value('AUTH_LOGIN')
auth_password = get_dotenv_value('AUTH_PASSWORD')
if auth_login and auth_password:
    app.config['BASIC_AUTH_USERNAME'] = auth_login
    app.config['BASIC_AUTH_PASSWORD'] = auth_password
    basic_auth = BasicAuth(app)

def create_agent_config():
    """Create agent configuration with Gemini models"""
    return AgentConfig(
        chat_model=models.ModelConfig(
            type=models.ModelType.CHAT,
            provider=models.ModelProvider.GEMINI,
            name="gemini-2.0-flash-exp",
            ctx_length=1000000
        ),
        utility_model=models.ModelConfig(
            type=models.ModelType.CHAT,
            provider=models.ModelProvider.GEMINI,
            name="gemini-1.5-flash",
            ctx_length=1000000
        ),
        embeddings_model=models.ModelConfig(
            type=models.ModelType.EMBEDDING,
            provider=models.ModelProvider.GEMINI,
            name="text-embedding-004"
        ),
        browser_model=models.ModelConfig(
            type=models.ModelType.CHAT,
            provider=models.ModelProvider.GEMINI,
            name="gemini-2.0-flash-exp",
            ctx_length=1000000
        ),
        mcp_servers=get_dotenv_value('MCP_SERVERS') or "",
        prompts_subdir=get_dotenv_value('PROMPTS_SUBDIR') or "",
        memory_subdir=get_dotenv_value('MEMORY_SUBDIR') or "",
        knowledge_subdirs=(get_dotenv_value('KNOWLEDGE_SUBDIRS') or "default,custom").split(','),
        code_exec_docker_enabled=get_dotenv_value('DOCKER_ENABLED') == 'true',
        code_exec_ssh_enabled=get_dotenv_value('SSH_ENABLED') == 'true',
        code_exec_ssh_addr=get_dotenv_value('SSH_ADDR') or 'localhost',
        code_exec_ssh_port=int(get_dotenv_value('SSH_PORT') or '55022'),
        code_exec_ssh_user=get_dotenv_value('SSH_USER') or 'root',
        code_exec_ssh_pass=get_dotenv_value('SSH_PASS') or ''
    )

# Global agent context
agent_context = None

@app.route('/')
def index():
    """Main chat interface"""
    return render_template('index.html')

@app.route('/api/chat', methods=['POST'])
def chat():
    """Handle chat messages"""
    global agent_context

    try:
        data = request.get_json()
        message = data.get('message', '')

        if not message:
            return jsonify({'error': 'No message provided'}), 400

        # Initialize agent context if needed
        if not agent_context:
            config = create_agent_config()
            agent_context = AgentContext(config=config)

        # Create user message
        user_msg = UserMessage(message=message)

        # Send message to agent
        task = agent_context.communicate(user_msg)

        # Get response (this is synchronous for now)
        try:
            response = task.result_sync()
            return jsonify({
                'response': response or 'Task completed',
                'status': 'success'
            })
        except Exception as e:
            return jsonify({
                'response': f'Error: {str(e)}',
                'status': 'error'
            })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/reset', methods=['POST'])
def reset():
    """Reset the agent context"""
    global agent_context
    if agent_context:
        agent_context.reset()
    return jsonify({'status': 'reset'})

if __name__ == '__main__':
    # Print startup information
    PrintStyle(font_color="green", padding=True).print("Starting Agent Zero with Gemini AI...")

    # Check for Gemini API key
    api_key = get_dotenv_value('GEMINI_API_KEY') or get_dotenv_value('GOOGLE_API_KEY')
    if not api_key or api_key == 'None':
        PrintStyle(font_color="red", padding=True).print(
            "ERROR: No Gemini API key found. Please set GEMINI_API_KEY in your .env file."
        )
        sys.exit(1)

    # Get host and port
    host = get_dotenv_value('WEB_UI_HOST') or 'localhost'
    port = int(get_dotenv_value('WEB_UI_PORT') or '50001')

    PrintStyle(font_color="blue", padding=True).print(f"Starting web interface at http://{host}:{port}")

    # Run the Flask app
    app.run(host=host, port=port, debug=False)