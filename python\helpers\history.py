from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage, SystemMessage
from enum import Enum

class MessageType(Enum):
    USER = "user"
    ASSISTANT = "assistant"
    TOOL = "tool"
    WARNING = "warning"

@dataclass
class MessageContent:
    """Content of a message"""
    text: str = ""
    data: Dict[str, Any] = field(default_factory=dict)

@dataclass
class Message:
    """A message in the conversation history"""
    is_user: bool
    content: Union[str, MessageContent]
    type: MessageType = MessageType.USER

    def output(self) -> List["OutputMessage"]:
        """Convert to output format"""
        if isinstance(self.content, str):
            text = self.content
        else:
            text = self.content.text

        return [OutputMessage(
            type=self.type,
            content=text,
            is_user=self.is_user
        )]

@dataclass
class OutputMessage:
    """Output message format"""
    type: MessageType
    content: str
    is_user: bool = False

class History:
    """Manages conversation history for an agent"""

    def __init__(self, agent: "Agent"):
        self.agent = agent
        self.messages: List[Message] = []

    def add_user_message(self, content: Union[str, MessageContent]) -> Message:
        """Add user message to history"""
        msg = Message(is_user=True, content=content, type=MessageType.USER)
        self.messages.append(msg)
        return msg

    def add_ai_message(self, content: Union[str, MessageContent]) -> Message:
        """Add AI message to history"""
        msg = Message(is_user=False, content=content, type=MessageType.ASSISTANT)
        self.messages.append(msg)
        return msg

    def add_tool_result(self, tool_name: str, result: str) -> Message:
        """Add tool result to history"""
        content = f"Tool '{tool_name}' result:\n{result}"
        msg = Message(is_user=False, content=content, type=MessageType.TOOL)
        self.messages.append(msg)
        return msg

    def add_warning(self, content: str) -> Message:
        """Add warning message to history"""
        msg = Message(is_user=False, content=content, type=MessageType.WARNING)
        self.messages.append(msg)
        return msg

    def output(self) -> List[OutputMessage]:
        """Get history in output format"""
        result = []
        for msg in self.messages:
            result.extend(msg.output())
        return result

    def clear(self):
        """Clear history"""
        self.messages.clear()

def output_langchain(messages: List[OutputMessage]) -> List[BaseMessage]:
    """Convert output messages to LangChain format"""
    result = []
    for msg in messages:
        if msg.type == MessageType.USER:
            result.append(HumanMessage(content=msg.content))
        elif msg.type in [MessageType.ASSISTANT, MessageType.TOOL, MessageType.WARNING]:
            result.append(AIMessage(content=msg.content))
    return result