<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Agent Zero - Gemini Edition</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #1a1a1a;
            color: #ffffff;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: #2d2d2d;
            padding: 1rem;
            border-bottom: 1px solid #444;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .header h1 {
            color: #4285f4;
            font-size: 1.5rem;
        }

        .reset-btn {
            background: #dc3545;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
        }

        .reset-btn:hover {
            background: #c82333;
        }

        .chat-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
        }

        .messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .message {
            max-width: 80%;
            padding: 1rem;
            border-radius: 8px;
            word-wrap: break-word;
        }

        .message.user {
            background: #4285f4;
            align-self: flex-end;
            margin-left: auto;
        }

        .message.assistant {
            background: #2d2d2d;
            border: 1px solid #444;
            align-self: flex-start;
        }

        .message.error {
            background: #dc3545;
            align-self: flex-start;
        }

        .input-container {
            padding: 1rem;
            background: #2d2d2d;
            border-top: 1px solid #444;
            display: flex;
            gap: 1rem;
        }

        .message-input {
            flex: 1;
            background: #1a1a1a;
            border: 1px solid #444;
            color: white;
            padding: 0.75rem;
            border-radius: 4px;
            font-size: 1rem;
        }

        .message-input:focus {
            outline: none;
            border-color: #4285f4;
        }

        .send-btn {
            background: #4285f4;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
        }

        .send-btn:hover:not(:disabled) {
            background: #3367d6;
        }

        .send-btn:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 1rem;
            color: #888;
        }

        pre {
            background: #000;
            padding: 1rem;
            border-radius: 4px;
            overflow-x: auto;
            margin: 0.5rem 0;
        }

        code {
            background: #333;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🤖 Agent Zero - Gemini Edition</h1>
        <button class="reset-btn" onclick="resetChat()">Reset Chat</button>
    </div>

    <div class="chat-container">
        <div class="messages" id="messages">
            <div class="message assistant">
                <strong>Agent Zero:</strong> Hello! I'm Agent Zero powered by Google Gemini AI. I can help you with coding, research, analysis, and many other tasks. What would you like me to help you with today?
            </div>
        </div>

        <div class="loading" id="loading">
            Agent is thinking...
        </div>

        <div class="input-container">
            <input type="text" class="message-input" id="messageInput" placeholder="Type your message here..." onkeypress="handleKeyPress(event)">
            <button class="send-btn" id="sendBtn" onclick="sendMessage()">Send</button>
        </div>
    </div>

    <script>
        const messagesContainer = document.getElementById('messages');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const loading = document.getElementById('loading');

        function addMessage(content, isUser = false, isError = false) {
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${isUser ? 'user' : (isError ? 'error' : 'assistant')}`;

            if (isUser) {
                messageDiv.innerHTML = `<strong>You:</strong> ${escapeHtml(content)}`;
            } else {
                messageDiv.innerHTML = `<strong>Agent Zero:</strong> ${formatMessage(content)}`;
            }

            messagesContainer.appendChild(messageDiv);
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function formatMessage(content) {
            // Basic markdown-like formatting
            content = escapeHtml(content);

            // Code blocks
            content = content.replace(/```(\w+)?\n([\s\S]*?)```/g, '<pre><code>$2</code></pre>');

            // Inline code
            content = content.replace(/`([^`]+)`/g, '<code>$1</code>');

            // Line breaks
            content = content.replace(/\n/g, '<br>');

            return content;
        }

        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;

            // Add user message
            addMessage(message, true);
            messageInput.value = '';

            // Show loading and disable input
            loading.style.display = 'block';
            sendBtn.disabled = true;
            messageInput.disabled = true;

            try {
                const response = await fetch('/api/chat', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ message: message })
                });

                const data = await response.json();

                if (data.error) {
                    addMessage(data.error, false, true);
                } else {
                    addMessage(data.response || 'No response received');
                }
            } catch (error) {
                addMessage(`Error: ${error.message}`, false, true);
            } finally {
                // Hide loading and enable input
                loading.style.display = 'none';
                sendBtn.disabled = false;
                messageInput.disabled = false;
                messageInput.focus();
            }
        }

        async function resetChat() {
            try {
                await fetch('/api/reset', { method: 'POST' });
                messagesContainer.innerHTML = `
                    <div class="message assistant">
                        <strong>Agent Zero:</strong> Chat has been reset. How can I help you today?
                    </div>
                `;
            } catch (error) {
                addMessage(`Error resetting chat: ${error.message}`, false, true);
            }
        }

        function handleKeyPress(event) {
            if (event.key === 'Enter' && !event.shiftKey) {
                event.preventDefault();
                sendMessage();
            }
        }

        // Focus input on load
        messageInput.focus();
    </script>
</body>
</html>