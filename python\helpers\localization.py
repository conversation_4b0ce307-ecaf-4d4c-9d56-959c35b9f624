from datetime import datetime
from typing import Optional

class Localization:
    """Localization utilities"""

    _instance: Optional["Localization"] = None

    def __init__(self):
        self.timezone = "UTC"
        self.date_format = "%Y-%m-%d %H:%M:%S"

    @classmethod
    def get(cls) -> "Localization":
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance

    def serialize_datetime(self, dt: datetime) -> str:
        """Serialize datetime to string"""
        return dt.strftime(self.date_format)

    def parse_datetime(self, dt_str: str) -> datetime:
        """Parse datetime from string"""
        return datetime.strptime(dt_str, self.date_format)