from python.helpers.extract_tools import Tool
from python.helpers.print_style import PrintStyle
import python.helpers.log as Log

class Response(Tool):
    """Tool for providing final response to user"""

    async def execute(self) -> str:
        """Execute the response tool"""
        response_text = self.args.get("text", self.args.get("response", ""))

        if not response_text:
            return "Error: No response text provided"

        # Log the response
        self.agent.context.log.log(
            type=Log.Type.INFO,
            heading="Final Response",
            content=response_text
        )

        # Print the response
        PrintStyle(font_color="green", padding=True).print(f"Response: {response_text}")

        return response_text